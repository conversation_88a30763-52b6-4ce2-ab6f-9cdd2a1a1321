'use client';

import React from 'react';
import { MapPin, Star, User } from 'lucide-react';
import { mockBarbers } from '@/data/mockBarbers';

export default function BarberGrid() {
  // Show only first 4 barbers for grid view
  const gridBarbers = mockBarbers.slice(0, 4);

  return (
    <div className="px-4 pb-6">
      {/* Section Title */}
      <div className="mb-4">
        <h2 className="text-lg font-medium text-gray-900">Nearby Barbers</h2>
      </div>
      <div className="grid grid-cols-2 gap-4">
        {gridBarbers.map((barber) => (
          <div key={barber.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
            {/* Image Container */}
            <div className="relative aspect-square bg-gray-100">
              {barber.isNew && (
                <div className="absolute top-3 left-3 z-10">
                  <span className="bg-white text-gray-600 text-xs font-medium px-2 py-1 rounded-full border">
                    NEW
                  </span>
                </div>
              )}
              
              {barber.image ? (
                <img 
                  src={barber.image} 
                  alt={barber.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <User className="w-12 h-12 text-gray-300" />
                </div>
              )}
            </div>

            {/* Content */}
            <div className="p-3">
              {/* Distance */}
              <p className="text-sm text-gray-500 mb-2">{barber.distance}</p>
              
              {/* Barber Name */}
              <h3 className="font-medium text-gray-900 mb-1 text-sm">{barber.name}</h3>
              
              {/* Shop Name */}
              <p className="text-xs text-gray-600 mb-2">{barber.shop}</p>
              
              {/* Location */}
              <div className="flex items-center text-xs text-gray-500 mb-3">
                <MapPin className="w-3 h-3 mr-1" />
                <span>{barber.location}</span>
              </div>

              {/* Rating */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-1">
                  <Star className="w-3 h-3 text-yellow-400 fill-current" />
                  <span className="text-xs text-gray-600">{barber.rating}</span>
                </div>
                <span className="text-xs text-gray-500">({barber.reviews})</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

'use client';

import React from 'react';
import { User } from 'lucide-react';
import { popularHairstyles } from '@/data/mockBarbers';

export default function PopularHairstyles() {
  return (
    <div className="px-4 pb-6">
      <div className="flex space-x-4 overflow-x-auto scrollbar-hide">
        {popularHairstyles.slice(0, 5).map((style) => (
          <div key={style.id} className="flex flex-col items-center space-y-2 min-w-0">
            {/* Circular Avatar */}
            <div className="w-16 h-16 rounded-full bg-gray-100 border-2 border-gray-200 flex items-center justify-center hover:border-gray-300 transition-colors cursor-pointer">
              {style.image ? (
                <img 
                  src={style.image} 
                  alt={style.name}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User className="w-8 h-8 text-gray-400" />
              )}
            </div>
            
            {/* Style Name */}
            <span className="text-xs text-gray-600 text-center font-medium whitespace-nowrap">
              {style.name}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}

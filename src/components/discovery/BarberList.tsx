'use client';

import React from 'react';
import { MapPin, Star, User } from 'lucide-react';
import { mockBarbers } from '@/data/mockBarbers';

export default function BarberList() {
  // Show different barbers for list view (starting from index 3)
  const listBarbers = mockBarbers.slice(3);

  return (
    <div className="px-4 space-y-4">
      {listBarbers.map((barber) => (
        <div key={barber.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
          <div className="flex">
            {/* Image Container */}
            <div className="relative w-24 h-24 bg-gray-100 flex-shrink-0">
              <div className="absolute top-2 left-2 z-10">
                <span className="bg-black text-white text-xs font-medium px-2 py-1 rounded">
                  5 min
                </span>
              </div>
              
              {barber.image ? (
                <img 
                  src={barber.image} 
                  alt={barber.name}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <User className="w-8 h-8 text-gray-300" />
                </div>
              )}
            </div>

            {/* Content */}
            <div className="flex-1 p-4">
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  {/* Barber Name */}
                  <h3 className="font-medium text-gray-900 mb-1">{barber.name}</h3>
                  
                  {/* Location */}
                  <div className="flex items-center text-sm text-gray-500 mb-2">
                    <MapPin className="w-4 h-4 mr-1" />
                    <span>{barber.location}</span>
                  </div>
                  
                  {/* Distance and Rating */}
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <span>{barber.distance}</span>
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span>{barber.rating}</span>
                    </div>
                  </div>
                </div>

                {/* Book Now Button */}
                <button className="bg-black text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-gray-800 transition-colors">
                  Book Now
                </button>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
